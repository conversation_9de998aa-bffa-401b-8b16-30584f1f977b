import { Component, OnInit, On<PERSON><PERSON>roy, AfterViewInit, ChangeDetectorRef } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ExpenseService } from '../../services/expense.service';
import { ExpenseDto } from '../../models/expenseDto.model';
import { ExpenseDashboardDto } from '../../models/expenseDashboardDto.model';
import { ExpensePagingParameters, ExpenseFilterState, ExpenseSortState } from '../../models/expensePagingParameters';
import { PaginatedResult } from '../../models/pagination';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { Observable, Subject } from 'rxjs'; // Observable eklendi
import { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { SingleResponseModel } from '../../models/singleResponseModel'; // SingleResponseModel eklendi
import { faEdit, faTrashAlt, faPlus, faFilter, faCalendarAlt, faSort, faSortUp, faSortDown, faFileExport, faFileExcel, faFilePdf } from '@fortawesome/free-solid-svg-icons';
import { ExpenseDialogComponent } from '../expense-dialog/expense-dialog.component';
import { DialogService } from '../../services/dialog.service';
import { Chart, registerables, ChartOptions, TooltipItem } from 'chart.js'; // Chart.js ve tipler eklendi
import * as XLSX from 'xlsx';

Chart.register(...registerables);

@Component({
  selector: 'app-expense-management',
  templateUrl:  './expense-management.component.html',
  styleUrls: ['./expense-management.component.css'],
  standalone: false
})
export class ExpenseManagementComponent implements OnInit, OnDestroy, AfterViewInit {

  // Pagination ve filtreleme
  paginatedExpenses: PaginatedResult<ExpenseDto> = {
    data: [],
    pageNumber: 1,
    pageSize: 20,
    totalCount: 0,
    totalPages: 0,
    hasPrevious: false,
    hasNext: false
  };

  isLoading = false;
  totalDailyExpense: number = 0;
  totalMonthlyExpense: number = 0;
  totalYearlyExpense: number = 0;

  // Filtreleme durumu
  filterState: ExpenseFilterState = {
    searchText: '',
    startDate: null,
    endDate: null,
    expenseType: '',
    minAmount: null,
    maxAmount: null,
    selectedYear: new Date().getFullYear(),
    selectedMonth: new Date().getMonth() + 1
  };

  // Sıralama durumu
  sortState: ExpenseSortState = {
    sortBy: 'ExpenseDate',
    sortDirection: 'desc'
  };

  // Eski değişkenler (geriye uyumluluk için)
  selectedYear: number;
  selectedMonth: number;
  initialYear: number;
  initialMonth: number;
  years: number[] = [];
  months: { value: number, name: string }[] = [
    { value: 1, name: 'Ocak' }, { value: 2, name: 'Şubat' }, { value: 3, name: 'Mart' },
    { value: 4, name: 'Nisan' }, { value: 5, name: 'Mayıs' }, { value: 6, name: 'Haziran' },
    { value: 7, name: 'Temmuz' }, { value: 8, name: 'Ağustos' }, { value: 9, name: 'Eylül' },
    { value: 10, name: 'Ekim' }, { value: 11, name: 'Kasım' }, { value: 12, name: 'Aralık' }
  ];

  searchControl = new FormControl('');

  // Gider türleri
  expenseTypes: string[] = [
    'Fatura - Elektrik', 'Fatura - Su', 'Fatura - Doğalgaz', 'Fatura - İnternet',
    'Maaş Ödemesi', 'Kira', 'Malzeme Alımı', 'Temizlik Malzemesi',
    'Ofis Gideri', 'Bakım/Onarım', 'Vergi/Harç', 'Diğer'
  ];

  // Icons
  faEdit = faEdit;
  faTrashAlt = faTrashAlt;
  faPlus = faPlus;
  faFilter = faFilter;
  faCalendarAlt = faCalendarAlt;
  faSort = faSort;
  faSortUp = faSortUp;
  faSortDown = faSortDown;
  faFileExport = faFileExport;
  faFileExcel = faFileExcel;
  faFilePdf = faFilePdf;

  private destroy$ = new Subject<void>();

  // Chart instances
  distributionChart: Chart | null = null;
  trendChart: Chart | null = null;
  monthlyTrendData: { labels: string[], data: number[] } = { labels: [], data: [] }; // Aylık trend verisi için
  // trendChart: Chart | null = null; // Duplicate kaldırıldı
  initialLoadComplete = false;

  // Template'de kullanım için
  Math = Math;

  // Performans iyileştirmeleri
  private searchSubject = new Subject<string>();
  private filterCache = new Map<string, PaginatedResult<ExpenseDto>>();
  private lastFilterParams: string = '';

  // Loading states
  isSearching = false;
  isExporting = false;

  // Spam koruması
  private lastSearchTime = 0;
  private searchCooldown = 1000; // 1 saniye cooldown
  private lastExportTime = 0;
  private exportCooldown = 3000; // 3 saniye cooldown
  private lastDialogOpenTime = 0;
  private dialogCooldown = 500; // 0.5 saniye cooldown

  // Favori filtreler kaldırıldı

  constructor(
    private expenseService: ExpenseService,
    private dialog: MatDialog,
    private toastrService: ToastrService,
    private dialogService: DialogService,
    private cdRef: ChangeDetectorRef
  ) {
    const currentDate = new Date();
    this.initialYear = currentDate.getFullYear();
    this.initialMonth = currentDate.getMonth() + 1;
    this.selectedYear = this.initialYear;
    this.selectedMonth = this.initialMonth;

    // Filter state'i başlat
    this.filterState.selectedYear = this.initialYear;
    this.filterState.selectedMonth = this.initialMonth;

    // Dinamik yıl listesi: mevcut yıldan 2 yıl sonrasına kadar, 5 yıl öncesine kadar
    const currentYear = new Date().getFullYear();
    const startYear = currentYear + 2; // 2 yıl sonrası
    const endYear = currentYear - 5;   // 5 yıl öncesi
    this.years = [];
    for (let i = startYear; i >= endYear; i--) {
      this.years.push(i);
    }
  }

  ngOnInit(): void {
    // Dashboard verilerini yükle
    this.loadDashboardData();

    // Sayfalanmış verileri yükle
    this.loadExpensesPaginated();

    // Otomatik arama kaldırıldı - sadece ara butonuyla arama yapılacak
    this.searchControl.valueChanges.pipe(
      takeUntil(this.destroy$)
    ).subscribe((value) => {
      this.filterState.searchText = value || '';
    });

    // Keyboard navigation
    this.setupKeyboardNavigation();

    // Favori filtreler kaldırıldı
  }

  private setupKeyboardNavigation(): void {
    document.addEventListener('keydown', this.handleKeyboardEvent);
  }

  ngAfterViewInit(): void {
    if (this.initialLoadComplete) {
       this.createOrUpdateCharts();
    }
  }


  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.destroyCharts();

    // Keyboard event listener'ı temizle
    document.removeEventListener('keydown', this.handleKeyboardEvent);
  }

  private handleKeyboardEvent = (event: KeyboardEvent) => {
    // Keyboard navigation logic
    if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
      event.preventDefault();
      this.openExpenseDialog();
    }

    if ((event.ctrlKey || event.metaKey) && event.key === 'e') {
      event.preventDefault();
      this.exportToExcel();
    }

    if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
      event.preventDefault();
      const searchInput = document.querySelector('.search-input') as HTMLInputElement;
      if (searchInput) {
        searchInput.focus();
      }
    }

    if (event.key === 'Escape') {
      if (this.hasActiveFilters()) {
        this.clearFilters();
      }
    }

    if (event.altKey) {
      if (event.key === 'ArrowLeft' && this.paginatedExpenses.hasPrevious) {
        event.preventDefault();
        this.goToPage(this.paginatedExpenses.pageNumber - 1);
      }
      if (event.key === 'ArrowRight' && this.paginatedExpenses.hasNext) {
        event.preventDefault();
        this.goToPage(this.paginatedExpenses.pageNumber + 1);
      }
    }
  }

  // OPTIMIZE: Yeni tek API çağrısı metodu - 5 API isteği yerine 1 API isteği
  loadDashboardData(): void {
    this.isLoading = true;

    this.expenseService.getDashboardData(this.selectedYear, this.selectedMonth)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            const dashboardData = response.data;

            // Tüm verileri tek seferde ata
            this.totalDailyExpense = dashboardData.totalDailyExpense;
            this.totalMonthlyExpense = dashboardData.totalMonthlyExpense;
            this.totalYearlyExpense = dashboardData.totalYearlyExpense;
            // Eski expenses property'si kaldırıldı, artık paginatedExpenses kullanıyoruz

            // Aylık trend verisi
            const labels: string[] = [];
            const data: number[] = [];
            for (let month = 1; month <= 12; month++) {
              const label = `${month.toString().padStart(2, '0')}/${this.selectedYear}`;
              labels.push(label);
              data.push(dashboardData.monthlyExpenseSummary[month] || 0);
            }
            this.monthlyTrendData = { labels, data };

            // Filtreleri uygula ve grafikleri oluştur
            this.applyFilters();
            this.initialLoadComplete = true;
            this.createOrUpdateCharts();

          } else {
            this.handleDashboardError(response.message || 'Dashboard verileri yüklenemedi.');
          }
          this.isLoading = false;
          this.cdRef.detectChanges();
        },
        error: (error) => {
          console.error('Error fetching dashboard data:', error);
          this.handleDashboardError('Dashboard verileri yüklenirken bir sunucu hatası oluştu.');
          this.isLoading = false;
          this.cdRef.detectChanges();
        }
      });
  }

  private handleDashboardError(message: string): void {
    this.toastrService.error(message, 'Hata');
    // Hata durumunda varsayılan değerleri ata
    this.totalDailyExpense = 0;
    this.totalMonthlyExpense = 0;
    this.totalYearlyExpense = 0;
    // Eski property'ler kaldırıldı, artık paginatedExpenses kullanıyoruz

    // Boş grafik verisi oluştur
    const labels: string[] = [];
    const data: number[] = [];
    for (let month = 1; month <= 12; month++) {
      labels.push(`${month.toString().padStart(2, '0')}/${this.selectedYear}`);
      data.push(0);
    }
    this.monthlyTrendData = { labels, data };

    this.createOrUpdateCharts();
  }





  // Performanslı pagination metodu (cache'li)
  loadExpensesPaginated(): void {
    this.isLoading = true;

    const parameters: ExpensePagingParameters = {
      pageNumber: this.paginatedExpenses.pageNumber,
      pageSize: this.paginatedExpenses.pageSize,
      searchText: this.filterState.searchText,
      // Sıralama frontend tarafında yapılacağı için API'ye gönderilmiyor
      sortBy: 'ExpenseDate', // Default sıralama
      sortDirection: 'desc', // Default sıralama
      startDate: this.filterState.startDate || undefined,
      endDate: this.filterState.endDate || undefined,
      expenseType: this.filterState.expenseType,
      minAmount: this.filterState.minAmount || undefined,
      maxAmount: this.filterState.maxAmount || undefined,
      isActive: true
    };

    // Cache key oluştur
    const cacheKey = this.generateCacheKey(parameters);

    // Cache'den kontrol et
    if (this.filterCache.has(cacheKey) && this.lastFilterParams === cacheKey) {
      const cachedData = this.filterCache.get(cacheKey)!;
      this.paginatedExpenses = cachedData;
      this.isLoading = false;
      this.cdRef.detectChanges();
      return;
    }

    this.expenseService.getExpensesPaginated(parameters)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            this.paginatedExpenses = response.data;

            // Sıralamayı uygula
            this.applySorting();

            // Cache'e kaydet (max 10 entry)
            if (this.filterCache.size >= 10) {
              const firstKey = this.filterCache.keys().next().value;
              if (firstKey) {
                this.filterCache.delete(firstKey);
              }
            }
            this.filterCache.set(cacheKey, response.data);
            this.lastFilterParams = cacheKey;
          } else {
            this.toastrService.error(response.message || 'Giderler yüklenemedi.', 'Hata');
          }
          this.isLoading = false;
          this.cdRef.detectChanges();
        },
        error: (error) => {
          console.error('Error loading paginated expenses:', error);
          this.toastrService.error('Giderler yüklenirken bir hata oluştu.', 'Hata');
          this.isLoading = false;
          this.cdRef.detectChanges();
        }
      });
  }

  private generateCacheKey(parameters: ExpensePagingParameters): string {
    return JSON.stringify({
      pageNumber: parameters.pageNumber,
      pageSize: parameters.pageSize,
      searchText: parameters.searchText || '',
      // Sıralama frontend tarafında olduğu için cache key'e dahil edilmiyor
      startDate: parameters.startDate?.toISOString() || '',
      endDate: parameters.endDate?.toISOString() || '',
      expenseType: parameters.expenseType || '',
      minAmount: parameters.minAmount || 0,
      maxAmount: parameters.maxAmount || 0
    });
  }

  resetPaginationAndLoad(): void {
    this.paginatedExpenses.pageNumber = 1;
    this.clearCache(); // Cache'i temizle
    this.loadExpensesPaginated();
  }

  private clearCache(): void {
    this.filterCache.clear();
    this.lastFilterParams = '';
  }

  // Eski applyFilters metodu (geriye uyumluluk için)
  applyFilters(): void {
    // Bu metot artık sadece dashboard için kullanılıyor
    // Pagination için resetPaginationAndLoad kullanılıyor
    this.createOrUpdateCharts();
    this.cdRef.detectChanges();
  }


  // Yeni filtreleme metotları
  onFilterChange(): void {
    // Dashboard verilerini güncelle
    this.filterState.selectedYear = this.selectedYear;
    this.filterState.selectedMonth = this.selectedMonth;
    this.loadDashboardData();

    // Pagination verilerini güncelle
    this.resetPaginationAndLoad();
  }

  // Gelişmiş filtreleri uygula (spam korumalı)
  applyAdvancedFilters(): void {
    const now = Date.now();
    if (now - this.lastSearchTime < this.searchCooldown) {
      this.toastrService.warning('Çok hızlı arama yapıyorsunuz. Lütfen bekleyin.', 'Uyarı');
      return;
    }

    this.lastSearchTime = now;
    this.isSearching = true;

    // Kısa bir delay ekleyerek kullanıcı deneyimini iyileştir
    setTimeout(() => {
      this.resetPaginationAndLoad();
      this.isSearching = false;
    }, 300);
  }

  onAdvancedFilterChange(): void {
    // Bu metot artık kullanılmıyor, sadece geriye uyumluluk için
    this.applyAdvancedFilters();
  }

  clearFilters(): void {
    // Temel filtreleri temizle
    this.selectedYear = this.initialYear;
    this.selectedMonth = this.initialMonth;
    this.searchControl.setValue('');

    // Gelişmiş filtreleri temizle
    this.filterState = {
      searchText: '',
      startDate: null,
      endDate: null,
      expenseType: '',
      minAmount: null,
      maxAmount: null,
      selectedYear: this.initialYear,
      selectedMonth: this.initialMonth
    };

    this.loadDashboardData();
    this.resetPaginationAndLoad();
  }

  clearSearch(): void {
    this.searchControl.setValue('');
    this.filterState.searchText = '';
    this.resetPaginationAndLoad();
  }

  hasActiveFilters(): boolean {
    return !!(
      this.filterState.searchText ||
      this.filterState.startDate ||
      this.filterState.endDate ||
      this.filterState.expenseType ||
      this.filterState.minAmount ||
      this.filterState.maxAmount ||
      this.selectedYear !== this.initialYear ||
      this.selectedMonth !== this.initialMonth
    );
  }

  // Sıralama metotları (frontend tarafında)
  sortBy(column: string): void {
    if (this.sortState.sortBy === column) {
      this.sortState.sortDirection = this.sortState.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortState.sortBy = column;
      this.sortState.sortDirection = 'desc';
    }

    // Frontend tarafında sıralama yap
    this.applySorting();
  }

  // Frontend tarafında sıralama uygula
  private applySorting(): void {
    if (!this.paginatedExpenses.data || this.paginatedExpenses.data.length === 0) {
      return;
    }

    const sortedData = [...this.paginatedExpenses.data].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (this.sortState.sortBy) {
        case 'ExpenseDate':
          aValue = new Date(a.expenseDate);
          bValue = new Date(b.expenseDate);
          break;
        case 'ExpenseType':
          aValue = a.expenseType?.toLowerCase() || '';
          bValue = b.expenseType?.toLowerCase() || '';
          break;
        case 'Amount':
          aValue = a.amount;
          bValue = b.amount;
          break;
        case 'Description':
          aValue = a.description?.toLowerCase() || '';
          bValue = b.description?.toLowerCase() || '';
          break;
        default:
          return 0;
      }

      if (aValue < bValue) {
        return this.sortState.sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return this.sortState.sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });

    // Sıralanmış veriyi güncelle
    this.paginatedExpenses = {
      ...this.paginatedExpenses,
      data: sortedData
    };
  }

  getSortIcon(column: string): any {
    if (this.sortState.sortBy !== column) {
      return this.faSort;
    }
    return this.sortState.sortDirection === 'asc' ? this.faSortUp : this.faSortDown;
  }

  // Sayfalama metotları
  goToPage(page: number): void {
    if (page >= 1 && page <= this.paginatedExpenses.totalPages) {
      this.paginatedExpenses.pageNumber = page;
      this.loadExpensesPaginated();
    }
  }

  changePageSize(size: number): void {
    this.paginatedExpenses.pageSize = size;
    this.resetPaginationAndLoad();
  }

  getPageNumbers(): number[] {
    const totalPages = this.paginatedExpenses.totalPages;
    const currentPage = this.paginatedExpenses.pageNumber;
    const pages: number[] = [];

    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  getMonthName(monthValue: number): string {
    return this.months.find(m => m.value === monthValue)?.name || '';
  }

  getBadgeClass(expenseType: string | null | undefined): string {
    if (!expenseType) return 'modern-badge-secondary';
    const typeLower = expenseType.toLowerCase();
    if (typeLower.includes('fatura')) return 'modern-badge-info';
    if (typeLower.includes('maaş')) return 'modern-badge-primary';
    if (typeLower.includes('kira')) return 'modern-badge-warning';
    if (typeLower.includes('malzeme')) return 'modern-badge-success';
    if (typeLower.includes('temizlik')) return 'modern-badge-info';
    if (typeLower.includes('ofis')) return 'modern-badge-secondary';
    if (typeLower.includes('bakım') || typeLower.includes('onarım')) return 'modern-badge-danger';
    if (typeLower.includes('vergi') || typeLower.includes('harç')) return 'modern-badge-warning';
    return 'modern-badge-secondary';
  }

  getExpenseTypeIcon(expenseType: string | null | undefined): string {
    if (!expenseType) return 'fas fa-question-circle';
    const typeLower = expenseType.toLowerCase();

    if (typeLower.includes('elektrik')) return 'fas fa-bolt';
    if (typeLower.includes('su')) return 'fas fa-tint';
    if (typeLower.includes('doğalgaz')) return 'fas fa-fire';
    if (typeLower.includes('internet')) return 'fas fa-wifi';
    if (typeLower.includes('maaş')) return 'fas fa-user-tie';
    if (typeLower.includes('kira')) return 'fas fa-home';
    if (typeLower.includes('malzeme')) return 'fas fa-boxes';
    if (typeLower.includes('temizlik')) return 'fas fa-broom';
    if (typeLower.includes('ofis')) return 'fas fa-building';
    if (typeLower.includes('bakım') || typeLower.includes('onarım')) return 'fas fa-tools';
    if (typeLower.includes('vergi') || typeLower.includes('harç')) return 'fas fa-file-invoice-dollar';
    if (typeLower.includes('diğer')) return 'fas fa-ellipsis-h';

    return 'fas fa-receipt';
  }

  getExpenseTypeTooltip(expenseType: string | null | undefined): string {
    if (!expenseType) return 'Gider türü belirtilmemiş';

    const typeLower = expenseType.toLowerCase();

    if (typeLower.includes('elektrik')) return 'Elektrik faturası gideri';
    if (typeLower.includes('su')) return 'Su faturası gideri';
    if (typeLower.includes('doğalgaz')) return 'Doğalgaz faturası gideri';
    if (typeLower.includes('internet')) return 'İnternet faturası gideri';
    if (typeLower.includes('maaş')) return 'Personel maaş ödemesi';
    if (typeLower.includes('kira')) return 'Kira ödemesi';
    if (typeLower.includes('malzeme')) return 'Malzeme alım gideri';
    if (typeLower.includes('temizlik')) return 'Temizlik malzemesi gideri';
    if (typeLower.includes('ofis')) return 'Ofis gideri';
    if (typeLower.includes('bakım') || typeLower.includes('onarım')) return 'Bakım ve onarım gideri';
    if (typeLower.includes('vergi') || typeLower.includes('harç')) return 'Vergi ve harç ödemesi';

    return `${expenseType} gideri`;
  }

  // Performanslı export metodu (spam korumalı)
  exportToExcel(): void {
    const now = Date.now();
    if (now - this.lastExportTime < this.exportCooldown) {
      this.toastrService.warning('Çok hızlı export yapıyorsunuz. Lütfen bekleyin.', 'Uyarı');
      return;
    }

    if (this.isExporting) {
      this.toastrService.warning('Export işlemi devam ediyor...', 'Uyarı');
      return;
    }

    this.lastExportTime = now;
    this.isExporting = true;
    this.toastrService.info('Excel dosyası hazırlanıyor...', 'Bilgi');

    const parameters: ExpensePagingParameters = {
      pageNumber: 1,
      pageSize: 10000, // Tüm verileri al
      searchText: this.filterState.searchText,
      // Export için default sıralama
      sortBy: 'ExpenseDate',
      sortDirection: 'desc',
      startDate: this.filterState.startDate || undefined,
      endDate: this.filterState.endDate || undefined,
      expenseType: this.filterState.expenseType,
      minAmount: this.filterState.minAmount || undefined,
      maxAmount: this.filterState.maxAmount || undefined,
      isActive: true
    };

    this.expenseService.getAllExpensesFiltered(parameters)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            // Async olarak Excel dosyasını oluştur
            setTimeout(() => {
              this.generateExcelFile(response.data);
              this.isExporting = false;
            }, 100);
          } else {
            this.toastrService.error('Export verileri alınamadı.', 'Hata');
            this.isExporting = false;
          }
        },
        error: (error) => {
          console.error('Error exporting to Excel:', error);
          this.toastrService.error('Excel export sırasında bir hata oluştu.', 'Hata');
          this.isExporting = false;
        }
      });
  }

  private generateExcelFile(expenses: ExpenseDto[]): void {
    const exportData = expenses.map(expense => ({
      'Gider Türü': expense.expenseType || '-',
      'Tutar (₺)': expense.amount,
      'Gider Tarihi': new Date(expense.expenseDate).toLocaleDateString('tr-TR'),
      'Açıklama': expense.description || '-',
      'Oluşturma Tarihi': new Date(expense.creationDate).toLocaleDateString('tr-TR')
    }));

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Giderler');

    // Sütun genişliklerini ayarla
    const colWidths = [
      { wch: 20 }, // Gider Türü
      { wch: 15 }, // Tutar
      { wch: 15 }, // Gider Tarihi
      { wch: 30 }, // Açıklama
      { wch: 15 }  // Oluşturma Tarihi
    ];
    ws['!cols'] = colWidths;

    const fileName = `Giderler_${new Date().toLocaleDateString('tr-TR').replace(/\./g, '_')}.xlsx`;
    XLSX.writeFile(wb, fileName);

    this.toastrService.success('Excel dosyası başarıyla indirildi.', 'Başarılı');
  }

  openExpenseDialog(expense?: ExpenseDto): void {
    const now = Date.now();
    if (now - this.lastDialogOpenTime < this.dialogCooldown) {
      return; // Sessizce engelle
    }

    this.lastDialogOpenTime = now;

    const dialogRef = this.dialog.open(ExpenseDialogComponent, {
      width: '600px',
      data: expense ? { ...expense } : null
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) { // Dialog başarıyla kapandıysa (kaydetme/güncelleme yapıldıysa)
        this.loadDashboardData();
        this.resetPaginationAndLoad();
      }
    });
  }






  deleteExpense(expense: ExpenseDto): void {
    // confirmGeneric yerine confirmExpenseDelete kullanıldı
    this.dialogService.confirmExpenseDelete(expense)
      .subscribe((result: boolean) => {
        if (result) {
          this.isLoading = true;
          this.expenseService.delete(expense.expenseID)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
              next: (response) => {
                if (response.success) {
                  this.toastrService.success('Gider başarıyla silindi.', 'Başarılı');
                  this.loadDashboardData();
                  this.resetPaginationAndLoad();
                } else {
                  this.toastrService.error(response.message || 'Gider silinemedi.', 'Hata');
                   this.isLoading = false;
                }
              },
              error: (error) => {
                console.error('Error deleting expense:', error);
                this.toastrService.error('Gider silinirken bir sunucu hatası oluştu.', 'Hata');
                this.isLoading = false;
              }
            });
        }
      });
  }

  // --- Chart Methods ---

  destroyCharts(): void {
    this.distributionChart?.destroy();
    this.trendChart?.destroy();
    this.distributionChart = null;
    this.trendChart = null;
  }

  createOrUpdateCharts(): void {
     if (!this.initialLoadComplete || typeof document === 'undefined') return;
     this.destroyCharts();
     this.createDistributionChart();
     this.createTrendChart();
     this.cdRef.detectChanges();
  }

  private getChartColors(): { backgroundColors: string[], borderColors: string[] } {
    const isDarkMode = document.body.getAttribute('data-theme') === 'dark';
    const lightColors = ['#4361ee', '#4cc9f0', '#ffc107', '#28a745', '#dc3545', '#6c757d', '#f72585'];
    const darkColors = ['#6d8eff', '#64b5f6', '#ffb74d', '#4caf50', '#f44336', '#a0a0a0', '#ff75a0'];
    const selectedColors = isDarkMode ? darkColors : lightColors;
    const backgroundColors = selectedColors.map(color => `${color}B3`); // %70 opaklık
    const borderColors = selectedColors;
    return { backgroundColors, borderColors };
  }

  createDistributionChart(): void {
    const canvas = document.getElementById('expenseDistributionChart') as HTMLCanvasElement;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Pagination verilerini kullan
    const expensesToAnalyze = this.paginatedExpenses.data.length > 0 ? this.paginatedExpenses.data : [];

    if (expensesToAnalyze.length === 0) {
      this.createEmptyDistributionChart(ctx);
      return;
    }

    const typeData = expensesToAnalyze.reduce((acc, expense) => {
      const type = expense.expenseType || 'Diğer';
      acc[type] = (acc[type] || 0) + expense.amount;
      return acc;
    }, {} as { [key: string]: number });

    const labels = Object.keys(typeData);
    const data = Object.values(typeData);
    const { backgroundColors, borderColors } = this.getChartColors();

    // Chart.js Options tipini tanımla
    const options: ChartOptions<'doughnut'> = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
             labels: {
               padding: 15,
               boxWidth: 12,
               usePointStyle: true,
             }
          },
          tooltip: {
            callbacks: {
              // TooltipItem tipini kullan
              label: (context: TooltipItem<'doughnut'>) => {
                let label = context.label || '';
                const value = context.parsed || 0;
                const data = context.chart.data.datasets[0].data as number[]; // Veri setini al
                const total = data.reduce((acc, current) => acc + current, 0); // Toplamı hesapla
                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0; // Yüzdeyi hesapla

                if (label) {
                  label += ': ';
                }
                // Tutarı formatla
                label += new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY' }).format(value);
                // Yüzdeyi ekle
                label += ` (${percentage}%)`;
                return label;
              }
            }
          },
          // Datalabels plugin kaldırıldığı için bu kısım silindi
        },
        animation: {
           animateScale: true,
           animateRotate: true,
           duration: 1000
        },
        onClick: (event, elements) => {
          if (this.distributionChart) {
            this.onChartClick(event, this.distributionChart);
          }
        },
        onHover: (event, elements) => {
          if (this.distributionChart) {
            this.onChartHover(event, this.distributionChart);
          }
        }
      };

    this.distributionChart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: labels,
        datasets: [{
          label: 'Gider Dağılımı',
          data: data,
          backgroundColor: backgroundColors.slice(0, labels.length),
          borderColor: borderColors.slice(0, labels.length),
          borderWidth: 2,
          hoverOffset: 12,
          hoverBorderWidth: 3
        }]
      },
      options: options // Tanımlanan options nesnesini kullan
    });
  }

  private createEmptyDistributionChart(ctx: CanvasRenderingContext2D): void {
    const { backgroundColors, borderColors } = this.getChartColors();

    this.distributionChart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: ['Veri Yok'],
        datasets: [{
          label: 'Gider Dağılımı',
          data: [1],
          backgroundColor: ['rgba(108, 117, 125, 0.3)'],
          borderColor: ['rgba(108, 117, 125, 0.5)'],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            enabled: false
          }
        },
        events: [] // Etkileşimi devre dışı bırak
      }
    });

    // Canvas üzerine metin ekle
    this.addEmptyStateText(ctx, 'Gösterilecek gider verisi yok');
  }

  private createEmptyTrendChart(ctx: CanvasRenderingContext2D): void {
    const emptyLabels = this.monthlyTrendData.labels.length > 0 ? this.monthlyTrendData.labels :
      ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz', 'Tem', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara'];
    const emptyData = new Array(emptyLabels.length).fill(0);

    this.trendChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: emptyLabels,
        datasets: [{
          label: 'Aylık Toplam Gider',
          data: emptyData,
          borderColor: 'rgba(108, 117, 125, 0.5)',
          backgroundColor: 'rgba(108, 117, 125, 0.1)',
          fill: true,
          tension: 0.3,
          pointRadius: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            display: false
          },
          x: {
            display: false
          }
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            enabled: false
          }
        },
        events: []
      }
    });

    this.addEmptyStateText(ctx, 'Gösterilecek trend verisi yok');
  }

  private addEmptyStateText(ctx: CanvasRenderingContext2D, text: string): void {
    const canvas = ctx.canvas;
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    ctx.save();
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.font = '16px Arial';
    ctx.fillStyle = 'rgba(108, 117, 125, 0.7)';
    ctx.fillText(text, centerX, centerY);
    ctx.restore();
  }

  createTrendChart(): void {
    const canvas = document.getElementById('monthlyTrendChart') as HTMLCanvasElement;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Aylık trend verisini kontrol et
    const labels = this.monthlyTrendData.labels;
    const data = this.monthlyTrendData.data;

    if (!labels.length || !data.length || data.every(value => value === 0)) {
      this.createEmptyTrendChart(ctx);
      return;
    }

    const { backgroundColors, borderColors } = this.getChartColors();

    // Trend hesaplama (önceki aya göre artış/azalış)
    const trendData = this.calculateTrendData(data);

    // Chart.js Options tipini tanımla (Çizgi grafik için 'line')
    const options: ChartOptions<'line'> = {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            // Y ekseni etiketlerini formatla
            callback: (value) => new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY', maximumFractionDigits: 0 }).format(value as number)
          }
        },
        x: {
           grid: {
              display: false // X ekseni grid çizgilerini gizle
           }
        }
      },
      plugins: {
        legend: {
          display: false // Tek dataset olduğu için legend'ı gizle
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#fff',
          bodyColor: '#fff',
          borderColor: borderColors[0] || '#4361ee',
          borderWidth: 1,
          callbacks: {
            label: (context: TooltipItem<'line'>) => {
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              if (context.parsed.y !== null) {
                label += new Intl.NumberFormat('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(context.parsed.y) + ' ₺';

                // Trend bilgisi ekle
                const monthIndex = context.dataIndex;
                if (monthIndex > 0 && trendData[monthIndex]) {
                  const trend = trendData[monthIndex];
                  const trendIcon = trend.isIncrease ? '↗️' : trend.isDecrease ? '↘️' : '➡️';
                  const trendText = trend.isIncrease ? 'artış' : trend.isDecrease ? 'azalış' : 'değişim yok';
                  label += ` (${trendIcon} %${Math.abs(trend.percentage).toFixed(1)} ${trendText})`;
                }
              }
              return label;
            },
            afterLabel: (context: TooltipItem<'line'>) => {
              const monthIndex = context.dataIndex;
              if (monthIndex > 0 && trendData[monthIndex]) {
                const trend = trendData[monthIndex];
                return `Önceki aya göre: ${new Intl.NumberFormat('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(trend.difference)} ₺`;
              }
              return '';
            }
          }
        }
      },
      hover: { // Hover ayarları
        mode: 'nearest',
        intersect: true
      },
      animation: {
         duration: 1000,
         easing: 'easeInOutQuad'
      },
      onClick: (event, elements) => {
        if (this.trendChart) {
          this.onChartClick(event, this.trendChart);
        }
      },
      onHover: (event, elements) => {
        if (this.trendChart) {
          this.onChartHover(event, this.trendChart);
        }
      }
    };

    // Yeni Chart nesnesini oluştur
    this.trendChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: 'Aylık Toplam Gider',
          data: data,
          borderColor: borderColors[0] || '#4361ee',
          backgroundColor: backgroundColors[0] || 'rgba(67, 97, 238, 0.1)',
          fill: true,
          tension: 0.4,
          pointBackgroundColor: borderColors[0] || '#4361ee',
          pointBorderColor: '#fff',
          pointBorderWidth: 2,
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: borderColors[0] || '#4361ee',
          pointHoverBorderWidth: 3,
          pointRadius: 5,
          pointHoverRadius: 8,
          borderWidth: 3,
          hoverBorderWidth: 4
        }]
      },
      options: options
    });
  }

  private calculateTrendData(data: number[]): Array<{
    percentage: number;
    difference: number;
    isIncrease: boolean;
    isDecrease: boolean;
  }> {
    const trends = [];

    for (let i = 0; i < data.length; i++) {
      if (i === 0) {
        trends.push({ percentage: 0, difference: 0, isIncrease: false, isDecrease: false });
        continue;
      }

      const current = data[i];
      const previous = data[i - 1];
      const difference = current - previous;

      let percentage = 0;
      if (previous !== 0) {
        percentage = (difference / previous) * 100;
      } else if (current > 0) {
        percentage = 100; // Önceki ay 0, bu ay pozitif
      }

      trends.push({
        percentage,
        difference,
        isIncrease: difference > 0,
        isDecrease: difference < 0
      });
    }

    return trends;
  }

  // Trend göstergeleri kaldırıldı





  // Grafik etkileşim metotları
  onChartClick(event: any, chart: Chart): void {
    const points = chart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, true);

    if (points.length) {
      const firstPoint = points[0];
      const label = chart.data.labels?.[firstPoint.index];
      const value = chart.data.datasets[firstPoint.datasetIndex].data[firstPoint.index];

      // Tıklanan veri noktası hakkında detay göster
      this.toastrService.info(`${label}: ${new Intl.NumberFormat('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(value as number)} ₺`, 'Grafik Detayı');
    }
  }

  // Grafik hover efektleri
  onChartHover(event: any, chart: Chart): void {
    const canvas = chart.canvas;
    if (canvas) {
      canvas.style.cursor = chart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, true).length > 0 ? 'pointer' : 'default';
    }
  }

  // Favori filtre metotları kaldırıldı





  // Hızlı filtreler kaldırıldı
}